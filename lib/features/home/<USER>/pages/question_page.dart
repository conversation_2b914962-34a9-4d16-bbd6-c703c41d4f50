import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/question_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/comment_card.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_questions_state.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';

@RoutePage()
class QuestionPage extends StatefulWidget {
  final entities.Form form;
  final num? taskId;

  const QuestionPage({
    super.key,
    required this.form,
    this.taskId,
  });

  @override
  State<QuestionPage> createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  // State for comment fields
  final Map<num, String?> _selectedCommentTypes = {};
  final Map<num, TextEditingController> _commentControllers = {};

  // State for question visibility based on conditional logic
  final Map<num, bool> _questionVisibility = {};

  // State for comment validation
  final Map<num, String?> _commentValidationErrors = {};

  // Get questions from the form
  List<entities.Question>? get questionItems => widget.form.questions;

  // Get visible questions based on conditional logic
  List<entities.Question> get visibleQuestions {
    if (questionItems == null) return [];

    final visible = questionItems!.where((question) {
      // Always show questions without questionId (like comment items)
      if (question.questionId == null) return true;
      // Show questions based on visibility state (default to true if not set)
      final isVisible = _questionVisibility[question.questionId!] ?? true;
      return isVisible;
    }).toList();

    debugPrint(
        'Total questions: ${questionItems!.length}, Visible questions: ${visible.length}');
    return visible;
  }

  @override
  void initState() {
    super.initState();
    if (widget.form.questions != null) {
      for (final question in widget.form.questions!) {
        if (question.isComment == true && question.questionId != null) {
          final questionId = question.questionId!;
          _commentControllers[questionId] = TextEditingController();
          // Initialize with null or a default first item if applicable
          _selectedCommentTypes[questionId] = null;

          // Add listener to clear validation errors when user types
          _commentControllers[questionId]!.addListener(() {
            if (_commentValidationErrors.containsKey(questionId)) {
              setState(() {
                _commentValidationErrors[questionId] = null;
              });
            }
          });
        }
      }
    }

    // Initialize question visibility and process conditional logic
    _initializeQuestionVisibility();
    _processQuestionConditionalLogic();

    // Load saved comment data
    _loadSavedCommentData();
  }

  @override
  void dispose() {
    _commentControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  /// Initialize question visibility based on conditional logic
  /// All questions should be visible by default
  void _initializeQuestionVisibility() {
    if (widget.form.questions == null) return;

    debugPrint(
        'Initializing visibility for ${widget.form.questions!.length} questions');

    for (final question in widget.form.questions!) {
      if (question.questionId == null) continue;

      // All questions are visible by default
      _questionVisibility[question.questionId!] = true;
    }

    debugPrint(
        'Initialized visibility for ${_questionVisibility.length} questions');

    // Apply restricted multi-parent question visibility logic
    _handleRestrictedMultiQuestionVisibility();
  }

  /// Process question conditional logic based on saved QuestionAnswer data from QPMDPage
  void _processQuestionConditionalLogic() {
    if (widget.taskId == null || widget.form.questions == null) return;

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        // Task not found in database
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        // Form not found in task
        return;
      }

      // Get all saved QuestionAnswer objects for this form
      final savedAnswers = formModel.questionAnswers;

      // Process each question's conditions
      for (final question in widget.form.questions!) {
        if (question.questionConditions == null ||
            question.questionConditions!.isEmpty) {
          continue;
        }

        // Process each condition for this question
        for (final condition in question.questionConditions!) {
          if (condition.measurementId == null ||
              condition.measurementOptionId == null ||
              condition.actionQuestionId == null ||
              condition.action == null) {
            continue;
          }

          // Find matching saved answer for this measurement
          final matchingAnswer = savedAnswers
              .where((answer) =>
                  answer.measurementId == condition.measurementId!.toInt() &&
                  answer.measurementOptionId ==
                      condition.measurementOptionId!.toInt())
              .firstOrNull;

          if (matchingAnswer != null) {
            // Apply the conditional action
            _applyQuestionConditionalAction(
              condition.actionQuestionId!,
              condition.action!,
            );
          }
        }
      }

      // Apply restricted multi-parent question visibility logic after processing conditions
      _handleRestrictedMultiQuestionVisibility();

      // Update UI after processing all conditional logic
      setState(() {});
    } catch (e) {
      // Error processing question conditional logic - silently continue
    }
  }

  /// Apply conditional action (show/hide) to a target question
  void _applyQuestionConditionalAction(num targetQuestionId, String action) {
    final previousVisibility = _questionVisibility[targetQuestionId] ?? true;

    if (action.toLowerCase() == 'appear') {
      _questionVisibility[targetQuestionId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      _questionVisibility[targetQuestionId] = false;
    }

    // Debug: Log visibility changes
    final newVisibility = _questionVisibility[targetQuestionId] ?? true;
    if (previousVisibility != newVisibility) {
      debugPrint(
          'Question $targetQuestionId visibility changed: $previousVisibility -> $newVisibility (action: $action)');
    }
  }

  /// Handle restricted multi question visibility logic
  /// If any question meets the restrictedMultiQuestion criteria:
  /// - Initially: Hide the restrictedMultiQuestion, show all other questions
  /// - After filling: Show ALL questions when any other question has saved counter value > 0
  void _handleRestrictedMultiQuestionVisibility() {
    if (widget.form.questions == null || widget.form.questions!.isEmpty) return;

    try {
      // Step 1: Identify restrictedMultiQuestion
      entities.Question? restrictedMultiQuestion;

      for (final question in widget.form.questions!) {
        if (question.isMulti == true &&
            question.multiMeasurementId != null &&
            question.multiMeasurementId != 0) {
          restrictedMultiQuestion = question;
          debugPrint(
              'Found restrictedMultiQuestion: ${question.questionId} - ${question.questionDescription}');
          break;
        }
      }

      // Step 2: If no restrictedMultiQuestion found, return early
      if (restrictedMultiQuestion == null) {
        debugPrint('No restrictedMultiQuestion found');
        return;
      }

      // Step 3: Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
      final hasOtherQuestionWithValue =
          _hasOtherQuestionWithCounterValue(restrictedMultiQuestion);
      debugPrint(
          'Other questions have counter value > 0: $hasOtherQuestionWithValue');

      // Step 4: Apply visibility rules
      if (hasOtherQuestionWithValue) {
        // Make ALL questions visible (including restrictedMultiQuestion) when other questions have value > 0
        for (final question in widget.form.questions!) {
          if (question.questionId != null) {
            _questionVisibility[question.questionId!] = true;
          }
        }
        debugPrint(
            'Made all questions visible due to other questions having counter value > 0');
      } else {
        // Initially: Hide the restrictedMultiQuestion, show all other questions
        for (final question in widget.form.questions!) {
          if (question.questionId != null) {
            if (question.questionId == restrictedMultiQuestion.questionId) {
              // Hide the restrictedMultiQuestion
              _questionVisibility[question.questionId!] = false;
            } else {
              // Show all other questions
              _questionVisibility[question.questionId!] = true;
            }
          }
        }
        debugPrint(
            'Hidden restrictedMultiQuestion, showing all other questions');
      }
    } catch (e) {
      debugPrint('Error in _handleRestrictedMultiQuestionVisibility: $e');
    }
  }

  /// Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
  /// Returns true if any other question has a counter value > 0 in the QuestionAnswer database
  bool _hasOtherQuestionWithCounterValue(
      entities.Question restrictedMultiQuestion) {
    if (widget.taskId == null || widget.form.questions == null) {
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.form.formId}');
        return false;
      }

      // Check all questions except the restrictedMultiQuestion
      for (final question in widget.form.questions!) {
        if (question.questionId == null ||
            question.questionId == restrictedMultiQuestion.questionId) {
          continue; // Skip the restrictedMultiQuestion itself
        }

        // Look for QuestionAnswer entries for this question with counter values
        final questionAnswers = formModel.questionAnswers
            .where(
                (answer) => answer.questionId == question.questionId!.toInt())
            .toList();

        for (final answer in questionAnswers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > 0) {
              debugPrint(
                  'Found counter value > 0 in other question: ${question.questionId}, value: $counterValue');
              return true;
            }
          }
        }
      }

      debugPrint('No other questions have counter value > 0');
      return false;
    } catch (e) {
      debugPrint('Error checking other questions counter values: $e');
      return false;
    }
  }

  /// Load saved comment data from database
  void _loadSavedCommentData() {
    if (widget.taskId == null || widget.form.formId == null) return;

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) return;

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) return;

      // Get saved comment answers
      final savedCommentAnswers = formModel.questionAnswers
          .where((qa) => qa.isComment == true)
          .toList();

      // Restore comment data
      for (final commentAnswer in savedCommentAnswers) {
        if (commentAnswer.questionId != null) {
          final questionId = commentAnswer.questionId!;

          // Restore text field value
          if (_commentControllers.containsKey(questionId) &&
              commentAnswer.measurementTextResult != null) {
            _commentControllers[questionId]!.text =
                commentAnswer.measurementTextResult!;
          }

          // Restore dropdown selection
          if (commentAnswer.commentTypeId != null) {
            // Find the comment type by ID
            final question = widget.form.questions?.firstWhere(
                (q) => q.questionId == questionId,
                orElse: () => entities.Question());

            if (question?.commentTypes != null) {
              final commentType = question!.commentTypes!.firstWhere(
                  (ct) => ct.commentTypeId == commentAnswer.commentTypeId,
                  orElse: () => entities.CommentType());

              if (commentType.commentType != null) {
                setState(() {
                  _selectedCommentTypes[questionId] = commentType.commentType;
                });
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading saved comment data: $e');
    }
  }

  /// Validate comment fields
  bool _validateCommentFields() {
    // Pre-condition check: Only proceed if form.min_qty == 0
    if (widget.form.minQty != 0) {
      return true; // Skip validation if min_qty is not 0
    }

    bool isValid = true;
    setState(() {
      _commentValidationErrors.clear();
    });

    if (widget.form.questions == null) return isValid;

    for (final question in widget.form.questions!) {
      if (question.isComment == true &&
          question.questionId != null &&
          question.isCommentMandatory == true) {
        final questionId = question.questionId!;
        final controller = _commentControllers[questionId];
        final selectedType = _selectedCommentTypes[questionId];

        // Check if comment has dropdown options
        final hasDropdownOptions =
            question.commentTypes != null && question.commentTypes!.isNotEmpty;

        // Validate based on comment type
        if (hasDropdownOptions) {
          // For dropdown comments, check if dropdown is selected
          if (selectedType == null || selectedType.isEmpty) {
            setState(() {
              _commentValidationErrors[questionId] = 'This field is required';
            });
            isValid = false;
          }
        }

        // Always validate text field for mandatory comments
        if (controller == null || controller.text.trim().isEmpty) {
          setState(() {
            _commentValidationErrors[questionId] = 'This field is required';
          });
          isValid = false;
        }
      }
    }

    return isValid;
  }

  /// Generate QuestionAnswer objects for comments
  List<entities.QuestionAnswer> _generateCommentAnswers() {
    final commentAnswers = <entities.QuestionAnswer>[];

    if (widget.form.questions == null) return commentAnswers;

    for (final question in widget.form.questions!) {
      if (question.isComment == true && question.questionId != null) {
        final questionId = question.questionId!;
        final controller = _commentControllers[questionId];
        final selectedType = _selectedCommentTypes[questionId];

        // Only save if there's actual content
        if ((controller != null && controller.text.trim().isNotEmpty) ||
            (selectedType != null && selectedType.isNotEmpty)) {
          // Find comment type ID if dropdown is selected
          num? commentTypeId;
          if (selectedType != null && question.commentTypes != null) {
            final commentType = question.commentTypes!.firstWhere(
                (ct) => ct.commentType == selectedType,
                orElse: () => entities.CommentType());
            commentTypeId = commentType.commentTypeId;
          }

          final questionAnswer = entities.QuestionAnswer(
            taskId: widget.taskId,
            formId: widget.form.formId,
            questionId: questionId,
            questionpartId: null,
            measurementId: null,
            measurementTypeId: null,
            measurementOptionId: null,
            measurementTextResult: controller?.text.trim(),
            isComment: true,
            commentTypeId: commentTypeId,
          );

          commentAnswers.add(questionAnswer);
        }
      }
    }

    return commentAnswers;
  }

  /// Save comment answers to database
  Future<bool> _saveCommentAnswersToDatabase(
      List<entities.QuestionAnswer> commentAnswers) async {
    if (widget.taskId == null || widget.form.formId == null) {
      debugPrint('TaskId or FormId is null, cannot save comment answers');
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.form.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task');
        return false;
      }

      // Convert QuestionAnswer entities to models
      final commentAnswerModels = commentAnswers
          .map((qa) => QuestionAnswerModel(
                taskId: qa.taskId?.toInt(),
                formId: qa.formId?.toInt(),
                questionId: qa.questionId?.toInt(),
                questionpartId: qa.questionpartId?.toInt(),
                measurementId: qa.measurementId?.toInt(),
                measurementTypeId: qa.measurementTypeId?.toInt(),
                measurementOptionId: qa.measurementOptionId?.toInt(),
                measurementTextResult: qa.measurementTextResult,
                isComment: qa.isComment,
                commentTypeId: qa.commentTypeId?.toInt(),
              ))
          .toList();

      // Save to database in a write transaction
      realm.write(() {
        // Remove existing comment answers for this form
        final existingCommentAnswers = formModel.questionAnswers
            .where((qa) => qa.isComment == true)
            .toList();

        for (final existingAnswer in existingCommentAnswers) {
          formModel.questionAnswers.remove(existingAnswer);
        }

        // Add new comment answers
        for (final newAnswer in commentAnswerModels) {
          formModel.questionAnswers.add(newAnswer);
        }
      });

      debugPrint(
          'Successfully saved ${commentAnswerModels.length} comment answers to database');
      return true;
    } catch (e) {
      debugPrint('Error saving comment answers to database: $e');
      return false;
    }
  }

  /// Handle save button press
  void _handleSave() async {
    if (_validateCommentFields()) {
      final commentAnswers = _generateCommentAnswers();
      debugPrint(
          'Generated ${commentAnswers.length} comment answers for saving');

      // Save comment answers to database
      final saveSuccess = await _saveCommentAnswersToDatabase(commentAnswers);

      if (mounted) {
        if (saveSuccess) {
          SnackBarService.success(
            context: context,
            message: 'Comments saved successfully!',
          );
          // Navigate back
          Navigator.of(context).pop();
        } else {
          SnackBarService.error(
            context: context,
            message: 'Failed to save comments. Please try again.',
          );
        }
      }
    } else {
      SnackBarService.error(
        context: context,
        message: 'Please fill in all required comment fields.',
      );
    }
  }

  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements, then loop through measurement_validations to check if any "required" key is true
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                return true;
              }
            }
          }
        }
      }
    } else if (question.isMulti != true) {
      // Check if isSupplementaryQuestion is true and isOneOption is true
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // Check if isOneOption is true (any questionpart_id contains "-")
        if (question.questionParts != null) {
          for (final questionPart in question.questionParts!) {
            if (questionPart.questionpartId != null &&
                questionPart.questionpartId.toString().contains("-")) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  /// Get camera icon info for a question based on photo_tags_two array
  Map<String, dynamic> _getCameraIconInfo(entities.Question question) {
    final result = {'show': false, 'isMandatory': false};

    if (question.photoTagsTwo == null || question.photoTagsTwo!.isEmpty) {
      return result;
    }

    result['show'] = true;

    // Check if any item has is_mandatory set to true
    for (final photoTag in question.photoTagsTwo!) {
      if (photoTag.isMandatory == true) {
        result['isMandatory'] = true;
        break;
      }
    }

    return result;
  }

  /// Check if question has a photo URL
  bool _hasPhotoUrl(entities.Question question) {
    return question.photoUrl != null && question.photoUrl!.isNotEmpty;
  }

  /// Handle question tap navigation - moved from QuestionCard
  void _handleQuestionTap(entities.Question question) async {
    final questionParts = question.questionParts ?? [];
    final hasSignature = question.hasSignature ?? false;
    final isMulti = question.isMulti ?? false;

    if (questionParts.length != 1 || hasSignature) {
      if (isMulti) {
        // Navigate to FQPDPage for multi questions
        await context.pushRoute(FQPDRoute(
          question: question,
          taskId: widget.taskId,
          formId: widget.form.formId,
        ));
      } else {
        // Navigate to SubHeaderPage for questions with multiple parts or signature
        await context.pushRoute(SubHeaderRoute(
          title: question.questionDescription ?? 'Details',
          questionParts: questionParts,
          question: question,
          taskId: widget.taskId,
          formId: widget.form.formId,
        ));
      }
    } else {
      // Navigate to QPMDPage for single measurement questions
      await context.pushRoute(QPMDRoute(
        question: question,
        questionPart: questionParts.first,
        taskId: widget.taskId,
        formId: widget.form.formId,
      ));
    }

    // Refresh page state after returning from navigation
    _refreshPageState();
  }

  /// Handle photo section tap navigation - moved from QuestionCard
  void _handleAddPhotosTap(entities.Question question) async {
    final showCameraIcon = question.photoTagsTwo?.isNotEmpty == true ||
        question.photoTagsThree?.isNotEmpty == true;

    if (showCameraIcon &&
        (question.photoTagsTwo?.isNotEmpty == true ||
            question.photoTagsThree?.isNotEmpty == true)) {
      // Navigate to MPTPage with question and level
      final level = question.photoTagsTwo?.isNotEmpty == true ? 2 : 3;
      await context.pushRoute(MPTRoute(
        taskId: widget.taskId?.toString(),
        formId: widget.form.formId?.toString(),
        questionId: question.questionId?.toString(),
        images: const [],
        question: question,
        level: level,
        questionPartId: null,
      ));

      // Refresh page state after returning from navigation
      _refreshPageState();
    }
  }

  /// Refresh page state after returning from navigation
  /// This updates progress indicators and question visibility based on latest data
  void _refreshPageState() {
    if (mounted) {
      // Reprocess conditional logic to update question visibility
      _processQuestionConditionalLogic();

      // Reload comment data in case it was updated
      _loadSavedCommentData();

      // Trigger UI rebuild to reflect updated progress and visibility
      setState(() {});
    }
  }

  /// Calculate progress for a question based on saved QuestionAnswer data
  Map<String, dynamic> _calculateQuestionProgress(entities.Question question) {
    double progress = 0.0;
    String progressText = '0 of 0';

    if (question.measurements == null || question.measurements!.isEmpty) {
      return {'progress': progress, 'progressText': progressText};
    }

    final totalMeasurements = question.measurements!.length;
    int answeredMeasurements = 0;

    if (widget.taskId != null &&
        widget.form.formId != null &&
        question.questionId != null) {
      try {
        final realm = RealmDatabase.instance.realm;

        // Find the task with the matching taskId
        final taskModel = realm.query<TaskDetailModel>(
            'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

        if (taskModel != null) {
          // Find the form with the matching formId
          final formModel = taskModel.forms
              .where((form) => form.formId == widget.form.formId!.toInt())
              .firstOrNull;

          if (formModel != null) {
            // Get saved answers for this question
            final savedAnswers = formModel.questionAnswers
                .where((answer) =>
                    answer.questionId == question.questionId!.toInt())
                .toList();

            // Count unique measurements that have been answered
            final answeredMeasurementIds = <int>{};
            for (final answer in savedAnswers) {
              if (answer.measurementId != null) {
                answeredMeasurementIds.add(answer.measurementId!);
              }
            }
            answeredMeasurements = answeredMeasurementIds.length;
          }
        }
      } catch (e) {
        // Error calculating progress - use default values
      }
    }

    // Calculate progress percentage
    progress =
        totalMeasurements > 0 ? answeredMeasurements / totalMeasurements : 0.0;
    progressText = '$answeredMeasurements of $totalMeasurements';

    return {'progress': progress, 'progressText': progressText};
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.form.formName ?? 'Questions',
        actions: [
          IconButton(
            icon: const Icon(
              Icons.save_rounded,
              color: AppColors.black,
            ),
            onPressed: _handleSave,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            visibleQuestions.isEmpty
                ? const EmptyQuestionsState()
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 8.0),
                    itemCount: visibleQuestions.length,
                    itemBuilder: (context, index) {
                      final question = visibleQuestions[index];

                      if (question.isComment == true &&
                          question.questionId != null) {
                        final questionId = question.questionId!;
                        // Ensure controller and selected value exists, though initState should handle this
                        if (!_commentControllers.containsKey(questionId)) {
                          _commentControllers[questionId] =
                              TextEditingController();
                        }
                        if (!_selectedCommentTypes.containsKey(questionId)) {
                          _selectedCommentTypes[questionId] = null;
                        }

                        return CommentCard(
                          question: question,
                          selectedCommentType:
                              _selectedCommentTypes[questionId],
                          commentController: _commentControllers[questionId]!,
                          onCommentTypeChanged: (String? newValue) {
                            setState(() {
                              _selectedCommentTypes[questionId] = newValue;
                              // Clear validation error when value changes
                              _commentValidationErrors[questionId] = null;
                            });
                          },
                          errorText: _commentValidationErrors[questionId],
                        );
                      } else {
                        // Calculate progress based on saved QuestionAnswer data
                        final progressData =
                            _calculateQuestionProgress(question);
                        final progress = progressData['progress'] as double;
                        final progressText =
                            progressData['progressText'] as String;

                        final isMandatory = _isQuestionMandatory(question);
                        final cameraInfo = _getCameraIconInfo(question);
                        final hasPhoto = _hasPhotoUrl(question);

                        return QuestionCard(
                          question: question,
                          progress: progress,
                          progressText: progressText,
                          isMandatory: isMandatory,
                          showCameraIcon: cameraInfo['show'] as bool,
                          isCameraMandatory: cameraInfo['isMandatory'] as bool,
                          hasPhotoUrl: hasPhoto,
                          taskId: widget.taskId,
                          formId: widget.form.formId,
                          onQuestionTap: () => _handleQuestionTap(question),
                          onPhotoSectionTap: () =>
                              _handleAddPhotosTap(question),
                        );
                      }
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const Gap(8);
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
